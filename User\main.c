#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Serial.h"
#include "Key.h"
#include "LED.h"
#include "String.h"
#include "stepmotor.h"

int main(void)
{
	OLED_Init();
//	LED_Init();
	Serial_Init();
	gpio_init();
	
	OLED_ShowString(1,1,"STEP");
	OLED_ShowString(3,1,"Motor");
	
//	Serial_TxPacket[0]=0x01;
//	Serial_TxPacket[1]=0x02;
//	Serial_TxPacket[2]=0x03;
//	Serial_TxPacket[3]=0x04;
	
//	Serial_SendPacket();
//	 Control1(553,0,0,6400,6400); //553
//	 Control2(3200,1000,1000,6400,3200); //553
	
	Control_5(100,6400,1);
	Control_6(100,6400,1);
	Control_2(200,6400,0);
	while(1) {
		// 主循环 - 可以添加按键控制重新画圆
	}
}
