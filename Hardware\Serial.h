#ifndef __SERIAL_H
#define __SERIAL_H

extern uint8_t Serial_RxFlag;

extern char Serial_RxPacket[];

#include "stdio.h"
char String[100];

void Serial_Init(void);

void Serial_SendByte(unsigned char Byte);

void Serial_SendArray(uint8_t *Array,uint16_t Length);

void Serial_SendString(char *String);

uint32_t Serial_Pow(uint32_t X,uint32_t Y);

void Serial_SendNumber(uint32_t Number,uint32_t Length);

void Serial_Print(char *Str,char *string,uint16_t num);

void Serial_Printf(char *format,...);
	
uint8_t Serial_GetRxFlag(void);

uint8_t Serial_GetRxData(void);

void Serial_SendPacket(void);

#endif
