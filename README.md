# 二维云台步进电机控制系统

## 项目概述
基于STM32F103C8的二维云台步进电机控制系统，支持画等边三角形功能。

## 硬件连接
- **X轴步进电机**:
  - 脉冲信号: PA6
  - 方向信号: PA7
- **Y轴步进电机**:
  - 脉冲信号: PB13
  - 方向信号: PB14

## 核心功能

### 基础控制函数

#### StepMotor_Init() - 初始化
```c
void StepMotor_Init(void);
```
初始化GPIO引脚配置。

#### X_Move() - X轴移动
```c
void X_Move(int32_t steps, int32_t delay, uint8_t direction);
```
- **steps**: 移动步数
- **delay**: 每步延时
- **direction**: 方向(DIR_POSITIVE/DIR_NEGATIVE)

#### Y_Move() - Y轴移动
```c
void Y_Move(int32_t steps, int32_t delay, uint8_t direction);
```
参数含义同X_Move()。

#### XY_Move() - 双轴同步移动
```c
void XY_Move(int32_t x_steps, int32_t y_steps, int32_t delay, uint8_t x_dir, uint8_t y_dir);
```
使用Bresenham算法实现双轴同步移动。

### 图形绘制函数

#### Draw_Triangle() - 画等边三角形
```c
void Draw_Triangle(int32_t side_length, int32_t delay);
```
- **side_length**: 三角形边长(步数)
- **delay**: 每步延时

**绘制原理**:
1. 第一条边：水平向右移动
2. 第二条边：左上方向(120度角)
3. 第三条边：右下方向回到起点

## 使用方法

1. **初始化系统**:
   ```c
   OLED_Init();
   Serial_Init();
   StepMotor_Init(); // 初始化步进电机
   ```

2. **画等边三角形**:
   ```c
   OLED_ShowString(2,1,"Drawing Triangle");
   Draw_Triangle(100, 5000); // 边长100步，延时5000
   OLED_ShowString(3,1,"Triangle Done!");
   ```

3. **基础移动控制**:
   ```c
   X_Move(100, 3000, DIR_POSITIVE);  // X轴正方向移动100步
   Y_Move(50, 3000, DIR_NEGATIVE);   // Y轴负方向移动50步
   XY_Move(100, 100, 3000, DIR_POSITIVE, DIR_POSITIVE); // 双轴同步移动
   ```

## 参数建议

### 边长/步数
- 小图形: 50-100步
- 中图形: 100-200步
- 大图形: 200-500步

### 延时(delay)
- 高速: 1000-3000
- 标准: 3000-6000
- 低速: 6000-10000

## 技术特点
- **模块化设计**: 基础函数独立，便于组合使用
- **精确控制**: 使用Bresenham算法确保直线精度
- **同步双轴**: 双轴同时控制，轨迹平滑
- **参数化**: 支持大小和速度调节
- **代码简洁**: 去除冗余，专注核心功能

## 扩展示例
基于基础函数可轻松扩展更多图形:
```c
// 画正方形
void Draw_Square(int32_t side_length, int32_t delay) {
    X_Move(side_length, delay, DIR_POSITIVE);  // 右
    Y_Move(side_length, delay, DIR_POSITIVE);  // 上
    X_Move(side_length, delay, DIR_NEGATIVE);  // 左
    Y_Move(side_length, delay, DIR_NEGATIVE);  // 下
}
```
