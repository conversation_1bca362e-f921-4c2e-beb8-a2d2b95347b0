# 二维舵机画圆控制系统

## 项目概述
基于STM32F103C8的二维舵机控制系统，支持画圆形轨迹功能。

## 硬件连接
- **X轴步进电机**:
  - 脉冲信号: PA6
  - 方向信号: PA7
- **Y轴步进电机**:
  - 脉冲信号: PB13  
  - 方向信号: PB14

## 新增功能

### 画等边三角形函数

#### Draw_Triangle() - 等边三角形绘制
```c
void Draw_Triangle(int32_t side_length, int32_t delay);
```
- **side_length**: 三角形边长(步数)
- **delay**: 每步延时(控制速度)

**使用示例**:
```c
Draw_Triangle(100, 5000); // 边长100步，延时5000
```

**绘制原理**:
1. 第一条边：水平向右移动
2. 第二条边：使用Bresenham算法画120度斜线(左上方向)
3. 第三条边：使用Bresenham算法画120度斜线(右下方向)回到起点

### 画圆函数

#### 1. Draw_Circle() - 基础画圆函数
```c
void Draw_Circle(int32_t radius, int32_t steps, int32_t delay);
```
- **radius**: 圆的半径(步数)
- **steps**: 圆周分割步数(越大越圆滑，建议50-200)
- **delay**: 每步延时(控制速度，数值越大速度越慢)

**使用示例**:
```c
Draw_Circle(50, 100, 5000); // 半径50步，100段分割，延时5000
```

#### 2. Draw_Circle_Smooth() - 平滑画圆函数 (推荐)
```c
void Draw_Circle_Smooth(int32_t radius, int32_t steps, int32_t delay);
```
- 使用同步双轴控制，画出更平滑的圆形
- 参数含义同基础版本
- 建议steps设置为100-360获得最佳效果

**使用示例**:
```c
Draw_Circle_Smooth(50, 120, 3000); // 平滑圆形，推荐参数
```

## 使用方法

1. **初始化系统**:
   ```c
   OLED_Init();
   Serial_Init();
   gpio_init(); // 初始化步进电机GPIO
   ```

2. **画三角形**:
   ```c
   // 显示状态
   OLED_ShowString(2,1,"Drawing Triangle");

   // 执行画三角形
   Draw_Triangle(100, 5000);

   // 完成提示
   OLED_ShowString(3,1,"Triangle Done!");
   ```

3. **画圆**:
   ```c
   // 显示状态
   OLED_ShowString(2,1,"Drawing Circle...");

   // 执行画圆
   Draw_Circle_Smooth(50, 120, 3000);

   // 完成提示
   OLED_ShowString(3,1,"Circle Complete!");
   ```

## 参数调节建议

### 三角形边长(side_length)
- 小三角形: 50-80步
- 中三角形: 100-150步
- 大三角形: 200-300步

### 圆形半径(radius)
- 小圆: 20-40步
- 中圆: 50-80步
- 大圆: 100-150步

### 分割步数(steps) - 仅圆形
- 粗糙圆形: 50-80步
- 标准圆形: 100-120步
- 精细圆形: 150-360步

### 延时(delay)
- 高速: 1000-2000
- 标准: 3000-5000
- 低速: 6000-10000

## 注意事项
1. 确保步进电机供电充足
2. 调节延时参数避免失步
3. 根据机械结构调整半径范围
4. 建议先用小参数测试，再逐步增大

## 扩展功能
可基于现有函数扩展更多图形:
- 正方形: 使用四条直线段
- 正多边形: 基于三角形原理扩展
- 椭圆: 修改X,Y轴半径比例
- 螺旋: 在圆形基础上增加半径变化

## 技术特点
- **精确控制**: 使用Bresenham算法确保直线精度
- **同步双轴**: 双轴同时控制，轨迹更平滑
- **参数化**: 所有图形支持大小和速度调节
- **模块化**: 每个图形独立函数，便于扩展
