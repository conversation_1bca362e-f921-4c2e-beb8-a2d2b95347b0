#include "stm32f10x.h"                  // Device header
#include "stm32f10x_gpio.h"             // GPIO库
#include "stm32f10x_rcc.h"              // RCC库
#include "stepmotor.h"
#include <math.h>                       // 数学库用于sin/cos计算
// ?? delay_cnt ?????,???????
void delay_cnt(int32_t delay);

// 绝对值函数
static int32_t abs(int32_t x) { return x < 0 ? -x : x; }

// ���μӼ��ٽṹ��
//typedef struct {
//    int32_t total_pulses;   // ��������
//    int32_t accel_steps;    // ���ٽ׶�������
//    int32_t decel_steps;    // ���ٽ׶�������
//    int32_t initial_delay;  // ��ʼ��ʱ
//    int32_t max_speed_delay;// ����ٶ���ʱ
//} TrapezoidConfig;

// ��ʼ��
void gpio_init(void)
{

	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	GPIO_InitTypeDef  GPIO_InitStructure3;
	GPIO_InitStructure3.GPIO_Pin =GPIO_Pin_5 |GPIO_Pin_6 | GPIO_Pin_7 ;
	GPIO_InitStructure3.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure3.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure3);
	GPIO_ResetBits(GPIOA,GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7);
	
	
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	GPIO_InitTypeDef  GPIO_InitStructure1;
	GPIO_InitStructure1.GPIO_Pin =GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14; 
	GPIO_InitStructure1.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure1.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure1);
	// ��ʼ���˿�Ϊ�͵�ƽ���
	GPIO_ResetBits(GPIOB,GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14);
}

//ͨ�����μӼ����������ɺ���
static void generate_trapezoidal_pulses(TrapezoidConfig cfg) {
    int32_t const_steps = cfg.total_pulses - cfg.accel_steps - cfg.decel_steps;
    
    // �����������������γ����ε����
    if (const_steps < 0) {
        const_steps = 0;
        cfg.accel_steps = cfg.total_pulses / 2;
        cfg.decel_steps = cfg.total_pulses - cfg.accel_steps;
    }

    for (int32_t i = 0; i < cfg.total_pulses; i++) {
        int32_t current_delay;
        
        // ���㵱ǰ��ʱ
        if (i < cfg.accel_steps) { // ����
            current_delay = cfg.initial_delay - 
                (i * (cfg.initial_delay - cfg.max_speed_delay)) / cfg.accel_steps;
        } else if (i < (cfg.accel_steps + const_steps)) { // ����
            current_delay = cfg.max_speed_delay;
        } else { // ����
            int32_t decel_index = i - (cfg.accel_steps + const_steps);
            current_delay = cfg.max_speed_delay + 
                (decel_index * (cfg.initial_delay - cfg.max_speed_delay)) / cfg.decel_steps;
        }

        // ��������

				GPIO_SetBits(GPIOA, GPIO_Pin_6 | GPIO_Pin_7);
				GPIO_SetBits(GPIOB, GPIO_Pin_13);
        delay_cnt(current_delay);
        

				GPIO_ResetBits(GPIOA, GPIO_Pin_6 | GPIO_Pin_7);
				GPIO_ResetBits(GPIOB, GPIO_Pin_13);
        delay_cnt(current_delay);
    }
}


void Control1(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay) {
											 
    GPIO_ResetBits(GPIOA, GPIO_Pin_7);


    TrapezoidConfig cfg = {
        .total_pulses = pulses,
        .accel_steps = accel_steps,
        .decel_steps = decel_steps,
        .initial_delay = initial_delay,
        .max_speed_delay = max_speed_delay
    };
    generate_trapezoidal_pulses(cfg);
}
										 
void Control2(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay) {
											 
    GPIO_ResetBits(GPIOB, GPIO_Pin_14);


    TrapezoidConfig cfg = {
        .total_pulses = pulses,
        .accel_steps = accel_steps,
        .decel_steps = decel_steps,
        .initial_delay = initial_delay,
        .max_speed_delay = max_speed_delay
    };
    generate_trapezoidal_pulses(cfg);
}

void Control_1(int32_t Circle,int32_t delay,int16_t Direction)
{
		int32_t i;
		if(Direction)
			GPIO_SetBits(GPIOA, GPIO_Pin_7);
		else
			GPIO_ResetBits(GPIOA, GPIO_Pin_7);
		for(i=0; i < Circle; i++)    
		{
			GPIO_SetBits(GPIOA, GPIO_Pin_6);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);
			delay_cnt(delay);			
		}
}

void Control_2(int32_t Circle,int32_t delay,int16_t Direction)
{
		int32_t i;
		if(Direction)
			GPIO_SetBits(GPIOB, GPIO_Pin_14);
		else
			GPIO_ResetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle; i++)    
		{
			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);			
		}
}

void Control_3(int32_t Circle,int32_t delay,float number)
{
		int32_t i;
		GPIO_ResetBits(GPIOA, GPIO_Pin_7);  
		GPIO_SetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle*number; i++)    
		{
			
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);        
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);			
		}
}

void Control_4(int32_t Circle,int32_t delay,float number)
{
		int32_t i;
		GPIO_SetBits(GPIOA, GPIO_Pin_7);  
		GPIO_SetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle*number; i++)    
		{
			
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);        
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);			
		}
}

void Control_5(int32_t Circle,int32_t delay,float number)
{
		int32_t i;
		GPIO_ResetBits(GPIOA, GPIO_Pin_7);  
		GPIO_SetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle*number/3.0; i++)    
		{
//			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);        
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);

			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);		
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);						
		}
}

void Control_6(int32_t Circle,int32_t delay,float number)
{
		int32_t i;
		GPIO_SetBits(GPIOA, GPIO_Pin_7);  
		GPIO_SetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle*number/3.0; i++)    
		{
			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);        
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);
			GPIO_SetBits(GPIOA, GPIO_Pin_6); 
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);  
			delay_cnt(delay);

			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);		
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);			
		}
}

/**
 * @brief 画圆函数 - 控制二维舵机画出圆形轨迹
 * @param radius 圆的半径(步数)
 * @param steps 圆周分割步数(越大越圆滑)
 * @param delay 每步延时(控制速度)
 * @retval 无
 */
void Draw_Circle(int32_t radius, int32_t steps, int32_t delay)
{
    int32_t i;
    float angle_step = 2.0f * 3.14159f / steps; // 角度步长
    int32_t prev_x = radius, prev_y = 0; // 起始位置
    int32_t curr_x, curr_y, delta_x, delta_y;

    for(i = 1; i <= steps; i++) {
        // 计算当前位置
        float angle = i * angle_step;
        curr_x = (int32_t)(radius * cosf(angle));
        curr_y = (int32_t)(radius * sinf(angle));

        // 计算位移增量
        delta_x = curr_x - prev_x;
        delta_y = curr_y - prev_y;

        // X轴移动
        if(delta_x != 0) {
            if(delta_x > 0) GPIO_SetBits(GPIOA, GPIO_Pin_7);   // 正方向
            else GPIO_ResetBits(GPIOA, GPIO_Pin_7);            // 负方向

            for(int32_t j = 0; j < abs(delta_x); j++) {
                GPIO_SetBits(GPIOA, GPIO_Pin_6);
                delay_cnt(delay);
                GPIO_ResetBits(GPIOA, GPIO_Pin_6);
                delay_cnt(delay);
            }
        }

        // Y轴移动
        if(delta_y != 0) {
            if(delta_y > 0) GPIO_SetBits(GPIOB, GPIO_Pin_14);  // 正方向
            else GPIO_ResetBits(GPIOB, GPIO_Pin_14);           // 负方向

            for(int32_t j = 0; j < abs(delta_y); j++) {
                GPIO_SetBits(GPIOB, GPIO_Pin_13);
                delay_cnt(delay);
                GPIO_ResetBits(GPIOB, GPIO_Pin_13);
                delay_cnt(delay);
            }
        }

        // 更新前一个位置
        prev_x = curr_x;
        prev_y = curr_y;
    }
}

/**
 * @brief 平滑画圆函数 - 使用同步双轴控制画出更平滑的圆形
 * @param radius 圆的半径(步数)
 * @param steps 圆周分割步数(建议100-360)
 * @param delay 每步延时(控制速度)
 * @retval 无
 */
void Draw_Circle_Smooth(int32_t radius, int32_t steps, int32_t delay)
{
    int32_t i;
    float angle_step = 2.0f * 3.14159f / steps; // 角度步长

    for(i = 0; i < steps; i++) {
        float angle = i * angle_step;

        // 计算当前角度的X,Y方向速度比例
        float dx = -radius * sinf(angle) * angle_step; // X方向速度
        float dy = radius * cosf(angle) * angle_step;  // Y方向速度

        // 计算步数比例
        int32_t x_steps = (int32_t)(abs(dx) + 0.5f);
        int32_t y_steps = (int32_t)(abs(dy) + 0.5f);
        int32_t max_steps = x_steps > y_steps ? x_steps : y_steps;

        if(max_steps == 0) max_steps = 1; // 防止除零

        // 设置方向
        if(dx > 0) GPIO_SetBits(GPIOA, GPIO_Pin_7);
        else GPIO_ResetBits(GPIOA, GPIO_Pin_7);

        if(dy > 0) GPIO_SetBits(GPIOB, GPIO_Pin_14);
        else GPIO_ResetBits(GPIOB, GPIO_Pin_14);

        // 同步执行步进
        int32_t x_counter = 0, y_counter = 0;
        for(int32_t step = 0; step < max_steps; step++) {
            int32_t x_pulse = 0, y_pulse = 0;

            // 判断是否需要X轴脉冲
            x_counter += x_steps;
            if(x_counter >= max_steps) {
                x_counter -= max_steps;
                x_pulse = 1;
            }

            // 判断是否需要Y轴脉冲
            y_counter += y_steps;
            if(y_counter >= max_steps) {
                y_counter -= max_steps;
                y_pulse = 1;
            }

            // 同时发送脉冲
            if(x_pulse) GPIO_SetBits(GPIOA, GPIO_Pin_6);
            if(y_pulse) GPIO_SetBits(GPIOB, GPIO_Pin_13);
            delay_cnt(delay);

            if(x_pulse) GPIO_ResetBits(GPIOA, GPIO_Pin_6);
            if(y_pulse) GPIO_ResetBits(GPIOB, GPIO_Pin_13);
            delay_cnt(delay);
        }
    }
}

/**
 * @brief 测试画圆功能 - 依次测试不同大小的圆
 * @param 无
 * @retval 无
 */
void Test_Circle_Functions(void)
{
    // 测试小圆
    Draw_Circle_Smooth(30, 100, 4000);
    delay_cnt(1000000); // 延时1秒

    // 测试中圆
    Draw_Circle_Smooth(50, 120, 3000);
    delay_cnt(1000000); // 延时1秒

    // 测试大圆
    Draw_Circle_Smooth(80, 150, 2500);
}
