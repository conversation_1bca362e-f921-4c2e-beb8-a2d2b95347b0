#include "stm32f10x.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_rcc.h"
#include "stepmotor.h"

// 延时函数声明
void delay_cnt(int32_t delay);

// 绝对值函数
static int32_t abs_val(int32_t x) { return x < 0 ? -x : x; }

/**
 * @brief 步进电机初始化
 * @param 无
 * @retval 无
 */
void StepMotor_Init(void)
{
	// 使能GPIOA和GPIOB时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB, ENABLE);

	// 配置X轴引脚(PA6, PA7)
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = X_PULSE_PIN | X_DIR_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(X_PULSE_PORT, &GPIO_InitStructure);

	// 配置Y轴引脚(PB13, PB14)
	GPIO_InitStructure.GPIO_Pin = Y_PULSE_PIN | Y_DIR_PIN;
	GPIO_Init(Y_PULSE_PORT, &GPIO_InitStructure);

	// 初始化引脚状态
	GPIO_ResetBits(X_PULSE_PORT, X_PULSE_PIN);
	GPIO_ResetBits(X_DIR_PORT, X_DIR_PIN);
	GPIO_ResetBits(Y_PULSE_PORT, Y_PULSE_PIN);
	GPIO_ResetBits(Y_DIR_PORT, Y_DIR_PIN);
}

/**
 * @brief X轴移动函数
 * @param steps 移动步数
 * @param delay 每步延时
 * @param direction 方向(DIR_POSITIVE/DIR_NEGATIVE)
 * @retval 无
 */
void X_Move(int32_t steps, int32_t delay, uint8_t direction)
{
	// 设置方向
	if(direction == DIR_POSITIVE) {
		GPIO_SetBits(X_DIR_PORT, X_DIR_PIN);
	} else {
		GPIO_ResetBits(X_DIR_PORT, X_DIR_PIN);
	}

	// 发送脉冲
	for(int32_t i = 0; i < steps; i++) {
		GPIO_SetBits(X_PULSE_PORT, X_PULSE_PIN);
		delay_cnt(delay);
		GPIO_ResetBits(X_PULSE_PORT, X_PULSE_PIN);
		delay_cnt(delay);
	}
}

/**
 * @brief Y轴移动函数
 * @param steps 移动步数
 * @param delay 每步延时
 * @param direction 方向(DIR_POSITIVE/DIR_NEGATIVE)
 * @retval 无
 */
void Y_Move(int32_t steps, int32_t delay, uint8_t direction)
{
	// 设置方向
	if(direction == DIR_POSITIVE) {
		GPIO_SetBits(Y_DIR_PORT, Y_DIR_PIN);
	} else {
		GPIO_ResetBits(Y_DIR_PORT, Y_DIR_PIN);
	}

	// 发送脉冲
	for(int32_t i = 0; i < steps; i++) {
		GPIO_SetBits(Y_PULSE_PORT, Y_PULSE_PIN);
		delay_cnt(delay);
		GPIO_ResetBits(Y_PULSE_PORT, Y_PULSE_PIN);
		delay_cnt(delay);
	}
}

/**
 * @brief 双轴同步移动函数 - 使用Bresenham算法实现
 * @param x_steps X轴步数
 * @param y_steps Y轴步数
 * @param delay 每步延时
 * @param x_dir X轴方向
 * @param y_dir Y轴方向
 * @retval 无
 */
void XY_Move(int32_t x_steps, int32_t y_steps, int32_t delay, uint8_t x_dir, uint8_t y_dir)
{
	// 设置方向
	if(x_dir == DIR_POSITIVE) {
		GPIO_SetBits(X_DIR_PORT, X_DIR_PIN);
	} else {
		GPIO_ResetBits(X_DIR_PORT, X_DIR_PIN);
	}

	if(y_dir == DIR_POSITIVE) {
		GPIO_SetBits(Y_DIR_PORT, Y_DIR_PIN);
	} else {
		GPIO_ResetBits(Y_DIR_PORT, Y_DIR_PIN);
	}

	// 使用Bresenham算法实现双轴同步移动
	int32_t dx = abs_val(x_steps);
	int32_t dy = abs_val(y_steps);
	int32_t max_steps = (dx > dy) ? dx : dy;

	if(max_steps == 0) return; // 防止除零

	int32_t x_counter = 0;
	int32_t y_counter = 0;

	for(int32_t i = 0; i < max_steps; i++) {
		uint8_t x_pulse = 0;
		uint8_t y_pulse = 0;

		// 判断是否需要X轴脉冲
		x_counter += dx;
		if(x_counter >= max_steps) {
			x_counter -= max_steps;
			x_pulse = 1;
		}

		// 判断是否需要Y轴脉冲
		y_counter += dy;
		if(y_counter >= max_steps) {
			y_counter -= max_steps;
			y_pulse = 1;
		}

		// 同时发送脉冲
		if(x_pulse) GPIO_SetBits(X_PULSE_PORT, X_PULSE_PIN);
		if(y_pulse) GPIO_SetBits(Y_PULSE_PORT, Y_PULSE_PIN);
		delay_cnt(delay);

		if(x_pulse) GPIO_ResetBits(X_PULSE_PORT, X_PULSE_PIN);
		if(y_pulse) GPIO_ResetBits(Y_PULSE_PORT, Y_PULSE_PIN);
		delay_cnt(delay);
	}
}

/**
 * @brief 画等边三角形函数
 * @param side_length 三角形边长(步数)
 * @param delay 每步延时
 * @retval 无
 */
void Draw_Triangle(int32_t side_length, int32_t delay)
{
	// 第一条边：水平向右移动
	X_Move(side_length, delay, DIR_POSITIVE);

	// 第二条边：左上方向(120度角)
	// 计算X、Y分量：X = -side_length/2, Y = side_length*sqrt(3)/2
	int32_t x_steps = side_length / 2;
	int32_t y_steps = (int32_t)(side_length * 0.866f); // sqrt(3)/2 ≈ 0.866
	XY_Move(x_steps, y_steps, delay, DIR_NEGATIVE, DIR_POSITIVE);

	// 第三条边：右下方向回到起点
	XY_Move(x_steps, y_steps, delay, DIR_POSITIVE, DIR_NEGATIVE);
}

/**
 * @brief 测试电机功能
 * @param 无
 * @retval 无
 */
void Test_Motors(void)
{
	// 测试X轴移动
	X_Move(50, 3000, DIR_POSITIVE);  // X轴正方向50步
	delay_cnt(500000);               // 延时0.5秒
	X_Move(50, 3000, DIR_NEGATIVE);  // X轴负方向50步
	delay_cnt(500000);

	// 测试Y轴移动
	Y_Move(50, 3000, DIR_POSITIVE);  // Y轴正方向50步
	delay_cnt(500000);
	Y_Move(50, 3000, DIR_NEGATIVE);  // Y轴负方向50步
	delay_cnt(500000);

	// 测试双轴同步移动
	XY_Move(50, 50, 3000, DIR_POSITIVE, DIR_POSITIVE); // 右上方向
	delay_cnt(500000);
	XY_Move(50, 50, 3000, DIR_NEGATIVE, DIR_NEGATIVE); // 左下方向
}




