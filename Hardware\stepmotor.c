#include "stm32f10x.h"                  // Device header
#include "stm32f10x_gpio.h"             // GPIO库
#include "stm32f10x_rcc.h"              // RCC库
#include "stepmotor.h"
#include <math.h>                       // 数学库用于sin/cos计算
// ?? delay_cnt ?????,???????
void delay_cnt(int32_t delay);

// 绝对值函数
static int32_t abs(int32_t x) { return x < 0 ? -x : x; }

// ���μӼ��ٽṹ��
//typedef struct {
//    int32_t total_pulses;   // ��������
//    int32_t accel_steps;    // ���ٽ׶�������
//    int32_t decel_steps;    // ���ٽ׶�������
//    int32_t initial_delay;  // ��ʼ��ʱ
//    int32_t max_speed_delay;// ����ٶ���ʱ
//} TrapezoidConfig;

// ��ʼ��
void gpio_init(void)
{

	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	GPIO_InitTypeDef  GPIO_InitStructure3;
	GPIO_InitStructure3.GPIO_Pin =GPIO_Pin_5 |GPIO_Pin_6 | GPIO_Pin_7 ;
	GPIO_InitStructure3.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure3.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure3);
	GPIO_ResetBits(GPIOA,GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7);
	
	
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	GPIO_InitTypeDef  GPIO_InitStructure1;
	GPIO_InitStructure1.GPIO_Pin =GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14; 
	GPIO_InitStructure1.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure1.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure1);
	// ��ʼ���˿�Ϊ�͵�ƽ���
	GPIO_ResetBits(GPIOB,GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14);
}

//ͨ�����μӼ����������ɺ���
static void generate_trapezoidal_pulses(TrapezoidConfig cfg) {
    int32_t const_steps = cfg.total_pulses - cfg.accel_steps - cfg.decel_steps;
    
    // �����������������γ����ε����
    if (const_steps < 0) {
        const_steps = 0;
        cfg.accel_steps = cfg.total_pulses / 2;
        cfg.decel_steps = cfg.total_pulses - cfg.accel_steps;
    }

    for (int32_t i = 0; i < cfg.total_pulses; i++) {
        int32_t current_delay;
        
        // ���㵱ǰ��ʱ
        if (i < cfg.accel_steps) { // ����
            current_delay = cfg.initial_delay - 
                (i * (cfg.initial_delay - cfg.max_speed_delay)) / cfg.accel_steps;
        } else if (i < (cfg.accel_steps + const_steps)) { // ����
            current_delay = cfg.max_speed_delay;
        } else { // ����
            int32_t decel_index = i - (cfg.accel_steps + const_steps);
            current_delay = cfg.max_speed_delay + 
                (decel_index * (cfg.initial_delay - cfg.max_speed_delay)) / cfg.decel_steps;
        }

        // ��������

				GPIO_SetBits(GPIOA, GPIO_Pin_6 | GPIO_Pin_7);
				GPIO_SetBits(GPIOB, GPIO_Pin_13);
        delay_cnt(current_delay);
        

				GPIO_ResetBits(GPIOA, GPIO_Pin_6 | GPIO_Pin_7);
				GPIO_ResetBits(GPIOB, GPIO_Pin_13);
        delay_cnt(current_delay);
    }
}


void Control1(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay) {
											 
    GPIO_ResetBits(GPIOA, GPIO_Pin_7);


    TrapezoidConfig cfg = {
        .total_pulses = pulses,
        .accel_steps = accel_steps,
        .decel_steps = decel_steps,
        .initial_delay = initial_delay,
        .max_speed_delay = max_speed_delay
    };
    generate_trapezoidal_pulses(cfg);
}
										 
void Control2(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay) {
											 
    GPIO_ResetBits(GPIOB, GPIO_Pin_14);


    TrapezoidConfig cfg = {
        .total_pulses = pulses,
        .accel_steps = accel_steps,
        .decel_steps = decel_steps,
        .initial_delay = initial_delay,
        .max_speed_delay = max_speed_delay
    };
    generate_trapezoidal_pulses(cfg);
}

void Control_1(int32_t Circle,int32_t delay,int16_t Direction)
{
		int32_t i;
		if(Direction)
			GPIO_SetBits(GPIOA, GPIO_Pin_7);
		else
			GPIO_ResetBits(GPIOA, GPIO_Pin_7);
		for(i=0; i < Circle; i++)    
		{
			GPIO_SetBits(GPIOA, GPIO_Pin_6);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOA, GPIO_Pin_6);
			delay_cnt(delay);			
		}
}

void Control_2(int32_t Circle,int32_t delay,int16_t Direction)
{
		int32_t i;
		if(Direction)
			GPIO_SetBits(GPIOB, GPIO_Pin_14);
		else
			GPIO_ResetBits(GPIOB, GPIO_Pin_14);
		for(i=0; i < Circle; i++)    
		{
			GPIO_SetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);           					
			GPIO_ResetBits(GPIOB, GPIO_Pin_13);
			delay_cnt(delay);			
		}
}

/**
 * @brief 画等边三角形函数
 * @param side_length 三角形边长(步数)
 * @param delay 每步延时
 * @retval 无
 */
void Draw_Triangle(int32_t side_length, int32_t delay)
{
	// 第一条边：水平向右移动
	X_Move(side_length, delay, DIR_POSITIVE);

	// 第二条边：左上方向(120度角)
	// 计算X、Y分量：X = -side_length/2, Y = side_length*sqrt(3)/2
	int32_t x_steps = side_length / 2;
	int32_t y_steps = (int32_t)(side_length * 0.866f); // sqrt(3)/2 ≈ 0.866
	XY_Move(x_steps, y_steps, delay, DIR_NEGATIVE, DIR_POSITIVE);

	// 第三条边：右下方向回到起点
	XY_Move(x_steps, y_steps, delay, DIR_POSITIVE, DIR_NEGATIVE);
}




