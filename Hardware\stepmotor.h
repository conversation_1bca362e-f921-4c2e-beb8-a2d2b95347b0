#ifndef __STEPMOTOR_H
#define __STEPMOTOR_H

#include "stm32f10x.h"

// ??????????
typedef struct {
    int32_t total_pulses;   // ????
    int32_t accel_steps;    // ???????
    int32_t decel_steps;    // ???????
    int32_t initial_delay;  // ????(??)
    int32_t max_speed_delay;// ??????
} TrapezoidConfig;

void gpio_init(void);
void Control_Forward(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay);
void Control_Backward(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                      int32_t initial_delay, int32_t max_speed_delay);
void Control_TurnLeft(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                      int32_t initial_delay, int32_t max_speed_delay);
void Control_TurnRight(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                       int32_t initial_delay, int32_t max_speed_delay);
void Control_Left(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                  int32_t initial_delay, int32_t max_speed_delay);
void Control_Right(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                   int32_t initial_delay, int32_t max_speed_delay);
void Control_up(int32_t Circle,int32_t delay,float number);
void Control_down(int32_t Circle,int32_t delay,float number);



void Control1(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay);
void Control2(int32_t pulses, int32_t accel_steps, int32_t decel_steps, 
                     int32_t initial_delay, int32_t max_speed_delay);

void Control_1(int32_t Circle,int32_t delay,int16_t Direction);
void Control_1(int32_t Circle,int32_t delay,int16_t Direction);
void Control_3(int32_t Circle,int32_t delay,float number);
void Control_4(int32_t Circle,int32_t delay,float number);

void Draw_Circle(int32_t radius, int32_t steps, int32_t delay); // 画圆函数
void Draw_Circle_Smooth(int32_t radius, int32_t steps, int32_t delay); // 平滑画圆函数
void Test_Circle_Functions(void); // 测试画圆功能
void Draw_Triangle(int32_t side_length, int32_t delay); // 画等边三角形函数

#endif

