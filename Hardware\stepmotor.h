#ifndef __STEPMOTOR_H
#define __STEPMOTOR_H

#include "stm32f10x.h"

// 引脚定义
#define X_PULSE_PIN    GPIO_Pin_6   // X轴脉冲引脚 PA6
#define X_DIR_PIN      GPIO_Pin_7   // X轴方向引脚 PA7
#define Y_PULSE_PIN    GPIO_Pin_13  // Y轴脉冲引脚 PB13
#define Y_DIR_PIN      GPIO_Pin_14  // Y轴方向引脚 PB14

#define X_PULSE_PORT   GPIOA
#define X_DIR_PORT     GPIOA
#define Y_PULSE_PORT   GPIOB
#define Y_DIR_PORT     GPIOB

// 方向定义
#define DIR_POSITIVE   1
#define DIR_NEGATIVE   0

// 基础函数
void StepMotor_Init(void);                                    // 初始化
void X_Move(int32_t steps, int32_t delay, uint8_t direction); // X轴移动
void Y_Move(int32_t steps, int32_t delay, uint8_t direction); // Y轴移动
void XY_Move(int32_t x_steps, int32_t y_steps, int32_t delay, uint8_t x_dir, uint8_t y_dir); // 双轴同步移动

// 图形绘制函数
void Draw_Triangle(int32_t side_length, int32_t delay);       // 画等边三角形

#endif

