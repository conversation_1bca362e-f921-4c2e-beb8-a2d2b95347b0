#include "stm32f10x.h"                  // Device header
#include "stdio.h"
#include "stdarg.h"

uint8_t Serial_RxFlag;
char Serial_RxPacket[100];

void Serial_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE); // 修正：使用正确的USART1时钟使能
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_AF_PP;//RX����ģʽҪ��������
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;  
	GPIO_Init(GPIOA,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_IPU;//TX���ģʽҪ�����򸡿�
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;  
	GPIO_Init(GPIOA,&GPIO_InitStructure);
	
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate=9600;//������
	USART_InitStructure.USART_HardwareFlowControl=USART_HardwareFlowControl_None;//����
	USART_InitStructure.USART_Mode=USART_Mode_Tx|USART_Mode_Rx;//ģʽ
	USART_InitStructure.USART_Parity=USART_Parity_No;//У��λ
	USART_InitStructure.USART_StopBits=USART_StopBits_1;//ֹͣУ��λ
	USART_InitStructure.USART_WordLength=USART_WordLength_8b;//�ֳ�
	USART_Init(USART1,&USART_InitStructure);
	
	USART_ITConfig(USART1,USART_IT_RXNE,ENABLE);//����RXNE��NVIC�����
	
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannel=USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority=1;
	NVIC_Init(&NVIC_InitStructure);
	
	
	USART_Cmd(USART1,ENABLE);
}

//�ַ�
void Serial_SendByte(unsigned char Byte)
{
	USART_SendData(USART1,Byte);
	while(USART_GetFlagStatus(USART1,USART_FLAG_TXE)==RESET);//���ͼĴ����ձ�־λ
}

//����
void Serial_SendArray(uint8_t *Array,uint16_t Length)
{
	unsigned char i;
	for(i=0;i<Length;i++)
	{
		Serial_SendByte(Array[i]);
	}
}

//�ַ���
void Serial_SendString(char *String)
{
	unsigned char i;
	for(i=0;String[i]!=0;i++)
	{
		Serial_SendByte(String[i]);
	}
}

//����
uint32_t Serial_Pow(uint32_t X,uint32_t Y)
{
	uint32_t Result=1;
	while(Y--)
	{
		Result*=X;
	}
	return Result;
}

void Serial_SendNumber(uint32_t Number,uint32_t Length)
{
	unsigned char i;
	for(i=0;i<Length;i++)
	{
		Serial_SendByte(Number/Serial_Pow(10,Length-i-1)%10+'0');
	}
}

//�����ض���
int fputc(int ch,FILE *f)
{
	Serial_SendByte(ch);
	return ch;
}

//Springf
void Serial_Printf(char *format,...)
{
	char String[100];
	va_list arg;          //����һ�������б�����
	va_start(arg,format); //��format���ܲ���������arg��
	vsprintf(String,format,arg);
	va_end(arg);         //�ͷŲ�����
	Serial_SendString(String);
}


//===ת��===

//�����Զ����
//uint8_t Serial_GetRxFlag(void)
//{
//	if(Serial_RxFlag==1)
//	{
//		Serial_RxFlag=0;
//		return 1;
//	}
//	return 0;
//}
//
//uint8_t Serial_GetRxData(void)
//{
//	return Serial_RxData;
//}

//void Serial_SendPacket(void)
//{
//	Serial_SendByte(0xFF);
//	Serial_SendArray(Serial_TxPacket,4);
//	Serial_SendByte(0xFF);
//	
//}


//�жϼ�⴮�ڽ���
void USART1_IRQHandler(void)
{
	static uint8_t RxState=0;
	static uint8_t pRxPacket=0;
	if(USART_GetITStatus(USART1,USART_IT_RXNE)==SET)
	{
		uint8_t RxData=USART_ReceiveData(USART1);
		USART_ClearITPendingBit(USART1,USART_IT_RXNE);
		if(RxState==0)
		{
			if(RxData=='@'&&Serial_RxFlag==0)
			{
				RxState=1;
				pRxPacket=0;
			}
		}
		else if(RxState==1)
		{
			if(RxData=='\r')
			{
				RxState=2;
			}
			else
			{
				Serial_RxPacket[pRxPacket]=RxData;
				pRxPacket++;
			}
		}
		else if(RxState==2)
		{
			if(RxData=='\n')
			{
				RxState=0;
				Serial_RxFlag=1;
				Serial_RxPacket[pRxPacket]='\0';
			}
		}
		USART_ClearITPendingBit(USART1,USART_IT_RXNE);
	}
}






















